import { InputManager } from '../input/InputManager.js';
import { PlayerShip } from '../entities/PlayerShip.js';
import { GameObjectManager } from '../utils/GameObjectManager.js';
import { LevelManager } from '../managers/LevelManager.js';
import { EnemyManager } from '../managers/EnemyManager.js';
import { BossWarpManager } from '../managers/BossWarpManager.js';
import { TokenEconomyManager } from '../managers/TokenEconomyManager.js';
import { RealityWarpManager } from '../managers/RealityWarpManager.js';
import { GenieInterface } from '../ui/GenieInterface.js';
import { PowerUpIndicator } from '../ui/PowerUpIndicator.js';
import { OrangeSDKManager } from '../managers/OrangeSDKManager.js';
import { WarpVFXSystem } from '../vfx/WarpVFXSystem.js';
import { BloomEffect } from '../vfx/BloomEffect.js';
import { ENEMY_TYPES } from '../config/gameConfig.js';

/**
 * Core Game Engine - Main game loop and state management
 * Implements fixed timestep game loop with 60 FPS target
 */
export class GameEngine {
    constructor(canvas, uiElement) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.uiElement = uiElement;
        this.stars = [];

        // Game state
        this.isRunning = false;
        this.isPaused = false;
        this.gameState = 'MENU'; // Possible values: 'MENU', 'GAME_PLAY', 'PAUSED', 'GAME_OVER'

        // Fixed timestep configuration
        this.targetFPS = 60;
        this.fixedTimeStep = 1000 / this.targetFPS; // 16.67ms per frame
        this.maxFrameTime = 250; // Maximum frame time to prevent spiral of death

        // Timing variables
        this.lastFrameTime = 0;
        this.accumulator = 0;
        this.currentTime = 0;

        // Performance tracking
        this.frameCount = 0;
        this.fpsTimer = 0;
        this.currentFPS = 0;

        // Enemy sprites
        this.enemySprites = new Map();

        // Player and wingman sprites
        this.playerSprite = null;
        this.wingmanSprite = null;

        // Managers that may be attached externally
        this.bossWarpManager = null; // Boss warp manager
        this.llmClient = null; // LLM client

        // Visual Effects Systems
        this.warpVFX = null;
        this._warpVFXRegistered = false;
        this.bloomEffect = null;

        // Bind methods
        this.gameLoop = this.gameLoop.bind(this);
        this.handleResize = this.handleResize.bind(this);
    }

    async init() {

        // Set up canvas properties
        this.setupCanvas();

        // Initialize game systems (placeholder for now)
        await this.initializeSystems();

        // Start the game loop (but game won't actually run until startGameplay is called)
        this.start();

        return Promise.resolve();
    }

    setupCanvas() {
        // Set up canvas for crisp pixel rendering
        this.ctx.imageSmoothingEnabled = false;

        // Set canvas size based on container
        this.handleResize();
        window.addEventListener('resize', this.handleResize);

    }

    initializeStarfield() {
        const starCount = 200;
        for (let i = 0; i < starCount; i++) {
            this.stars.push({
                x: Math.random() * this.canvas.width,
                y: Math.random() * this.canvas.height,
                size: Math.random() * 1.5 + 0.5,
                speed: (Math.random() * 25) + 5 // pixels per second
            });
        }
    }

    async initializeSystems() {
        // Initialize input manager
        this.inputManager = new InputManager(this.canvas, this);

        // Initialize game object manager
        this.gameObjectManager = new GameObjectManager();

        // Initialize player ship at bottom center of screen
        const startX = this.canvas.width / 2;
        const startY = this.canvas.height - 100; // 100 pixels from bottom
        this.playerShip = new PlayerShip(startX, startY, this.canvas.width, this.canvas.height, this.gameObjectManager);
        
        // Add player ship to game object manager for collision detection
        this.gameObjectManager.add(this.playerShip);

        // Initialize level manager
        this.levelManager = new LevelManager(this.gameObjectManager);

        // Initialize enemy manager
        this.enemyManager = new EnemyManager(this.canvas.width, this.canvas.height, this.gameObjectManager);
        
        // Load enemy sprites
        await this.loadEnemySprites();
        
        // Load player and wingman sprites
        await this.loadPlayerAndWingmanSprites();
        
        // Create token economy manager first
        this.tokenManager = new TokenEconomyManager();
        
        // Create reality warp manager with proper dependencies (LLM client now handled by backend)
        this.realityWarpManager = new RealityWarpManager(this.tokenManager, null, this.levelManager);
        this.realityWarpManager.init();
        
        // Initialize boss warp manager
        this.bossWarpManager = new BossWarpManager(this.realityWarpManager, this.enemyManager);
        this.bossWarpManager.init();
        
        // Connect boss warp manager with enemy manager
        this.enemyManager.setBossWarpManager(this.bossWarpManager);
        
        // Environment service is now handled by backend API
        // Connect environment with enemy manager
        // Note: Environment will be set after it's loaded from the server

        // Initialize Orange SDK manager
        this.orangeSDKManager = new OrangeSDKManager();

        // Initialize Genie interface
        this.genieInterface = new GenieInterface(this.tokenManager, this);

        // Initialize power-up indicator
        this.powerUpIndicator = new PowerUpIndicator();
        this.powerUpIndicator.initialize();

        // Initialize Warp VFX system (purely visual)
        this.warpVFX = new WarpVFXSystem(this.canvas, this);
        // Enable VFX Dev tools (hotkeys) in debug mode
        if (this.warpVFX && (window.AuthManager?.config?.debugMode || false)) {
            this.warpVFX.initDevControls();
        }

        // Initialize Bloom Effect system
        this.bloomEffect = new BloomEffect(this.canvas, {
            enabled: true,
            intensity: 0.6,
            threshold: 0.5,
            blurRadius: 12,
            downsample: 2
        });

        // Add bloom debug controls (B key to toggle, +/- to adjust intensity)
        this.initBloomDebugControls();


        // Set up level manager callbacks
        this.setupLevelManagerCallbacks();

        // Set up enemy manager callbacks
        this.setupEnemyManagerCallbacks();
        
        // Set up boss warp manager callbacks
        this.setupBossWarpManagerCallbacks();

        // Set up Orange SDK manager callbacks
        this.setupOrangeSDKCallbacks();

        // Set up token manager callbacks
        this.setupTokenManagerCallbacks();

        // Initialize Genie interface
        await this.genieInterface.initialize();
        this.setupGenieInterfaceCallbacks();

        // Set up default environment with the enemy manager
        this.enemyManager.setEnvironment({
            getEnvironmentType: () => 'space',
            getCurrentGameplayModifiers: () => ({}),
            getCurrentEnvironmentType: () => 'space',
            getCurrentEnvironmentName: () => 'Default Environment',
            getCurrentEnvironmentDescription: () => 'Default space environment',
            isEnemyCompatible: () => true,
            getCurrentEnvironmentHazards: () => [],
            applyEnvironmentEffects: (stats) => stats,
            update: () => {},
            render: () => {},
            resetToDefault: () => {}
        });

        // Initialize Orange SDK (async)
        this.initializeOrangeSDK();

        // Initialize starfield (fallback if environment system fails)
        this.initializeStarfield();


        // Note: Level is NOT started here - it will be started when player clicks "Start Game"



    }

    /**
     * Load enemy sprites from assets/sprites/enemies directory
     * @returns {Promise<void>}
     */
    async loadEnemySprites() {
        const spritePaths = {
            [ENEMY_TYPES.WATER]: 'assets/sprites/enemies/water.png',
            [ENEMY_TYPES.FIRE]: 'assets/sprites/enemies/fire.png',
            [ENEMY_TYPES.AIR]: 'assets/sprites/enemies/air.png',
            [ENEMY_TYPES.EARTH]: 'assets/sprites/enemies/earth.png',
            [ENEMY_TYPES.CRYSTAL]: 'assets/sprites/enemies/crystal.png',
            [ENEMY_TYPES.SHADOW]: 'assets/sprites/enemies/shadow.png'
        };

        const loadPromises = Object.entries(spritePaths).map(([enemyType, path]) => {
            return new Promise((resolve) => {
                const img = new Image();
                img.crossOrigin = 'anonymous';
                
                img.onload = () => {
                    this.enemySprites.set(enemyType, img);
                    console.log(`✅ Loaded sprite for ${enemyType} enemy`);
                    resolve();
                };
                
                img.onerror = () => {
                    console.warn(`⚠️ Could not load sprite for ${enemyType} enemy at ${path}, will use fallback rendering`);
                    resolve(); // Still resolve so other sprites can load
                };
                
                img.src = path;
            });
        });

        await Promise.all(loadPromises);
        console.log('✅ All enemy sprites loaded');
    }

    /**
     * Load player and wingman sprites
     * @returns {Promise<void>}
     */
    async loadPlayerAndWingmanSprites() {
        // Load player sprite (using crystal.png enemy sprite as requested)
        this.playerSprite = await this.loadSprite('assets/sprites/enemies/crystal.png', 'player');
        
        // Load wingman sprite (using fire.png enemy sprite as requested)
        this.wingmanSprite = await this.loadSprite('assets/sprites/enemies/fire.png', 'wingman');
        
        console.log('✅ Player and wingman sprites loaded');
    }

    /**
     * Helper method to load a single sprite
     * @param {string} path - Path to the sprite image
     * @param {string} name - Name for logging purposes
     * @returns {Promise<Image>} Loaded image
     */
    loadSprite(path, name) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.crossOrigin = 'anonymous';
            
            img.onload = () => {
                console.log(`✅ Loaded ${name} sprite: ${path}`);
                resolve(img);
            };
            
            img.onerror = () => {
                console.warn(`⚠️ Could not load ${name} sprite at ${path}`);
                resolve(null); // Resolve with null instead of rejecting to avoid breaking the game
            };
            
            img.src = path;
        });
    }

    start() {
        if (!this.isRunning) {
            this.isRunning = true;
            this.isPaused = false;
            this.currentTime = performance.now();
            this.lastFrameTime = this.currentTime;
            this.accumulator = 0;
            this.frameCount = 0;
            this.fpsTimer = 0;
            requestAnimationFrame(this.gameLoop);
        }
    }

    /**
     * Start the actual gameplay (called when player clicks "Start Game")
     * @param {object} user - Authenticated user data
     */
    startGameplay(user = null) {
        this.gameState = 'GAME_PLAY';

        // Start the first level
        if (this.levelManager) {
            this.levelManager.startLevel(1);
        }

        // Show power-up indicator during gameplay
        if (this.powerUpIndicator) {
            this.powerUpIndicator.show();
        }

    }

    /**
     * Start the actual gameplay (called when player clicks "Start Game")
     * @param {object} user - Authenticated user data
     */
    startGame(user = null) {

        // Start the first level
        if (this.levelManager) {
            this.levelManager.startLevel(1);
        }

        // Show power-up indicator during gameplay
        if (this.powerUpIndicator) {
            this.powerUpIndicator.show();
        }

    }

    pause() {
        if (this.gameState === 'GAME_PLAY') {
            this.gameState = 'PAUSED';
            this.isPaused = true;

            // Notify Orange SDK of pause
            if (this.orangeSDKManager) {
                this.orangeSDKManager.handleGamePause();
            }
        }
    }

    resume() {
        if (this.gameState === 'PAUSED') {
            this.gameState = 'GAME_PLAY';
            this.isPaused = false;
            // Reset timing to prevent large delta time jump
            this.currentTime = performance.now();
            this.lastFrameTime = this.currentTime;
            this.accumulator = 0;

            // Notify Orange SDK of resume
            if (this.orangeSDKManager) {
                this.orangeSDKManager.handleGameResume();
            }
        }
    }

    async destroy() {
        this.isRunning = false;

        // Handle Orange SDK quit before cleanup
        if (this.orangeSDKManager) {
            await this.orangeSDKManager.handleGameQuit();
        }

        // Cleanup input manager
        if (this.inputManager) {
            this.inputManager.destroy();
        }

        // End reality warp when game engine is destroyed
        if (this.realityWarpManager) {
            const warpState = this.realityWarpManager.getWarpState();
            if (warpState.status === 'active') {
                this.realityWarpManager.endWarp();
            }
        }

        // Cleanup bloom effect
        if (this.bloomEffect) {
            this.bloomEffect.destroy();
        }

    }

    gameLoop(currentTime) {
        if (!this.isRunning) return;

        // Calculate frame time and clamp to prevent spiral of death
        let frameTime = currentTime - this.lastFrameTime;
        if (frameTime > this.maxFrameTime) {
            frameTime = this.maxFrameTime;
        }

        this.lastFrameTime = currentTime;

        if (!this.isPaused && this.gameState === 'GAME_PLAY') {
            // Add frame time to accumulator
            this.accumulator += frameTime;

            // Fixed timestep updates - run as many updates as needed
            while (this.accumulator >= this.fixedTimeStep) {
                this.update(this.fixedTimeStep);
                this.accumulator -= this.fixedTimeStep;
            }

            // Calculate interpolation factor for smooth rendering
            const interpolation = this.accumulator / this.fixedTimeStep;

            // Always render (variable timestep for smooth visuals)
            this.render(interpolation);

            // Update FPS counter
            this.updateFPSCounter(frameTime);
        } else {
            // Still render even when paused or in menu (for menus, etc.)
            this.render(0);
        }

        requestAnimationFrame(this.gameLoop);
    }

    update(deltaTime) {
        this.updateStarfield(deltaTime);

        // Only update game logic when in GAME_PLAY state
        if (this.gameState === 'GAME_PLAY') {
            // Update input manager
            if (this.inputManager) {
                this.inputManager.update();
            }

            // Update player ship with movement input (handle separately from GameObjectManager)
            if (this.playerShip && this.inputManager) {
                const movementInput = this.inputManager.getMovementVector();
                this.playerShip.update(deltaTime, movementInput);

                // Handle weapon firing input
                if (this.inputManager.isActionDown('fire')) {
                    this.playerShip.fire();
                }

                // Debug: Test damage system (D key)
                if (this.inputManager.isKeyPressed('KeyD')) {
                    this.playerShip.takeDamage(25); // Take 25 damage for testing
                }

                // Debug: Test healing system (H key)
                if (this.inputManager.isKeyPressed('KeyH')) {
                    this.playerShip.heal(25); // Heal 25 health for testing
                }

                // Debug: Add extra life (L key)
                if (this.inputManager.isKeyPressed('KeyL')) {
                    this.playerShip.addLives(1); // Add 1 life for testing
                }

                // Debug: Add tokens (T key)
                if (this.inputManager.isKeyPressed('KeyT')) {
                    this.tokenManager.awardTokens(500, 'debug_tokens');
                }

                // Debug: Show Genie interface (G key)
                if (this.inputManager.isKeyPressed('KeyG')) {
                    this.showGenieInterface({ levelNumber: 1, nextLevel: 2 });
                }

                // Debug: Test power-up system (P key)
                if (this.inputManager.isKeyPressed('KeyP')) {
                    this.testPowerUpSystem();
                }
            }

            // Update active power-ups
            if (this.genieInterface) {
                this.genieInterface.updateActivePowerUps(deltaTime);

                // Update power-up indicator with current active power-ups
                if (this.powerUpIndicator) {
                    this.powerUpIndicator.updateActivePowerUps(this.genieInterface.activePowerUps);
                    this.powerUpIndicator.update(deltaTime);
                }
            }

            // Update level manager
            if (this.levelManager) {
                const gameState = {
                    playerDestroyed: this.playerShip ? this.playerShip.getHealthStatus().isDestroyed : false,
                    playerDamageTaken: false, // This would be set by damage events
                    shotsFired: 0, // This would be tracked by weapon system
                    shotsHit: 0 // This would be tracked by collision system
                };
                this.levelManager.update(deltaTime, gameState);
            }

            // Update enemy manager (only if level is in progress)
            if (this.enemyManager && this.playerShip && this.levelManager && this.levelManager.levelInProgress) {
                const playerPosition = this.playerShip.position;
                this.enemyManager.update(deltaTime, playerPosition);

                // Handle collisions
                this.handleCollisions();
            }

            // Update all other game objects (projectiles, enemies, etc.)
            if (this.gameObjectManager) {
                this.gameObjectManager.update(deltaTime);
            }

            // Clean up out-of-bounds projectiles
            this.cleanupProjectiles();

            // Update Orange SDK auto-save
            if (this.orangeSDKManager) {
                this.orangeSDKManager.updateAutoSave();
            }
            
            // Update Boss Warp Manager
            if (this.bossWarpManager) {
                this.bossWarpManager.update(deltaTime);
            }
        }

        // Update Warp VFX (purely visual) - always update for visual effects
        if (this.warpVFX) {
            this.warpVFX.update(deltaTime);
        }
    }

    /**
     * Clean up projectiles that are out of bounds
     */
    cleanupProjectiles() {
        if (!this.gameObjectManager) return;

        const bounds = {
            left: -50,
            right: this.canvas.width + 50,
            top: -50,
            bottom: this.canvas.height + 50
        };

        const projectiles = this.gameObjectManager.findByTag('projectile');
        for (const projectile of projectiles) {
            if (projectile.isOutOfBounds(bounds)) {
                this.gameObjectManager.returnToPool('projectile', projectile);
            }
        }
    }

    updateStarfield(deltaTime) {
        const dtSeconds = deltaTime / 1000;
        for (const star of this.stars) {
            star.y += star.speed * dtSeconds;
            if (star.y > this.canvas.height) {
                star.y = 0;
                star.x = Math.random() * this.canvas.width;
            }
        }
    }

    render(interpolation = 0) {
        // Clear canvas
        this.ctx.fillStyle = '#000011';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // Render environment background (replaces starfield)
        let customBackgroundRendered = false;
        
        // Check for custom environment from level configuration first
        if (this.levelManager && this.levelManager.levelConfig && this.levelManager.levelConfig.environmentData) {
            const levelEnv = this.levelManager.levelConfig.environmentData;
            if (levelEnv.imageUrl) {
                const imageUrl = levelEnv.imageUrl;
                
                // Load and render custom background image
                // Only create a new Image object if the URL has changed
                if (!this.customBackgroundImage || this.customBackgroundImage.currentUrl !== imageUrl) {
                    //console.log('🔍 [GAME ENGINE DEBUG] Attempting to load image from level config:', imageUrl);
                    this.customBackgroundImage = new Image();
                    this.customBackgroundImage.crossOrigin = 'anonymous';
                    this.customBackgroundImage.currentUrl = imageUrl; // Store the current URL
                    this.customBackgroundImage.onload = () => {
                        //console.log('✅ [GAME ENGINE] Custom background image loaded successfully from level config');
                        //console.log('🔍 [GAME ENGINE DEBUG] Image loaded successfully - dimensions:', this.customBackgroundImage.naturalWidth, 'x', this.customBackgroundImage.naturalHeight);
                    };
                    this.customBackgroundImage.onerror = (error) => {
                        //console.error('❌ [GAME ENGINE] Failed to load custom background image from level config:', error);
                        //console.log('🔍 [GAME ENGINE DEBUG] Image load failed for URL:', imageUrl);
                        this.customBackgroundImage = null;
                    };
                    this.customBackgroundImage.src = imageUrl;
                }
                
                if (this.customBackgroundImage) {
                    //console.log('🔍 [GAME ENGINE DEBUG] Image loading status:', this.customBackgroundImage.complete ? 'complete' : 'loading');
                    //console.log('🔍 [GAME ENGINE DEBUG] Image natural dimensions:', this.customBackgroundImage.naturalWidth, 'x', this.customBackgroundImage.naturalHeight);
                }
                
                // Render custom background if image is loaded or if we have a cached image
                if (this.customBackgroundImage && this.customBackgroundImage.complete) {
                    // Render custom background
                    this.ctx.save();
                    
                    // Calculate scaling to cover entire canvas while maintaining aspect ratio
                    const imageAspect = this.customBackgroundImage.naturalWidth / this.customBackgroundImage.naturalHeight;
                    const canvasAspect = this.canvas.width / this.canvas.height;
                    
                    let drawWidth, drawHeight, drawX, drawY;
                    
                    if (imageAspect > canvasAspect) {
                        // Image is wider than canvas
                        drawHeight = this.canvas.height;
                        drawWidth = drawHeight * imageAspect;
                        drawX = (this.canvas.width - drawWidth) / 2;
                        drawY = 0;
                    } else {
                        // Image is taller than canvas
                        drawWidth = this.canvas.width;
                        drawHeight = drawWidth / imageAspect;
                        drawX = 0;
                        drawY = (this.canvas.height - drawHeight) / 2;
                    }
                    
                    this.ctx.drawImage(this.customBackgroundImage, drawX, drawY, drawWidth, drawHeight);
                    this.ctx.restore();
                    
                    customBackgroundRendered = true;
                } else if (this.customBackgroundImage && !this.customBackgroundImage.complete) {
                    // Image is still loading, but we want to show the custom background
                    // We'll show a placeholder or just not render the starfield
                    customBackgroundRendered = true;
                }
            }
        }
        
        // Fallback to RealityWarpManager environment if no level config environment
        if (!customBackgroundRendered && this.realityWarpManager && this.realityWarpManager.currentEnvironment &&
            this.realityWarpManager.getWarpState().status === 'active') {
            //console.log('🔍 [GAME ENGINE DEBUG] Reality warp is active, checking for custom environment');
            //console.log('🔍 [GAME ENGINE DEBUG] Warp state:', this.realityWarpManager.getWarpState());
            //console.log('🔍 [GAME ENGINE DEBUG] Current environment:', this.realityWarpManager.currentEnvironment);
            const customEnv = this.realityWarpManager.currentEnvironment;
            if (customEnv.imageData && customEnv.imageData.images && customEnv.imageData.images[0]) {
                const imageData = customEnv.imageData.images[0];
                const imageUrl = imageData.localUrl || imageData.url;
                
                // Load and render custom background image
                // Only create a new Image object if the URL has changed
                if (!this.customBackgroundImage || this.customBackgroundImage.currentUrl !== imageUrl) {
                    //console.log('🔍 [GAME ENGINE DEBUG] Attempting to load image from RealityWarpManager:', imageUrl);
                    this.customBackgroundImage = new Image();
                    this.customBackgroundImage.crossOrigin = 'anonymous';
                    this.customBackgroundImage.currentUrl = imageUrl; // Store the current URL
                    this.customBackgroundImage.onload = () => {
                        //console.log('✅ [GAME ENGINE] Custom background image loaded successfully from RealityWarpManager');
                        //console.log('🔍 [GAME ENGINE DEBUG] Image loaded successfully - dimensions:', this.customBackgroundImage.naturalWidth, 'x', this.customBackgroundImage.naturalHeight);
                    };
                    this.customBackgroundImage.onerror = (error) => {
                        //console.error('❌ [GAME ENGINE] Failed to load custom background image from RealityWarpManager:', error);
                        //console.log('🔍 [GAME ENGINE DEBUG] Image load failed for URL:', imageUrl);
                        this.customBackgroundImage = null;
                    };
                    this.customBackgroundImage.src = imageUrl;
                }
                
                if (this.customBackgroundImage) {
                    //console.log('🔍 [GAME ENGINE DEBUG] Image loading status:', this.customBackgroundImage.complete ? 'complete' : 'loading');
                    //console.log('🔍 [GAME ENGINE DEBUG] Image natural dimensions:', this.customBackgroundImage.naturalWidth, 'x', this.customBackgroundImage.naturalHeight);
                }
                
                // Render custom background if image is loaded or if we have a cached image
                if (this.customBackgroundImage && this.customBackgroundImage.complete) {
                    // Render custom background
                    this.ctx.save();
                    
                    // Calculate scaling to cover entire canvas while maintaining aspect ratio
                    const imageAspect = this.customBackgroundImage.naturalWidth / this.customBackgroundImage.naturalHeight;
                    const canvasAspect = this.canvas.width / this.canvas.height;
                    
                    let drawWidth, drawHeight, drawX, drawY;
                    
                    if (imageAspect > canvasAspect) {
                        // Image is wider than canvas
                        drawHeight = this.canvas.height;
                        drawWidth = drawHeight * imageAspect;
                        drawX = (this.canvas.width - drawWidth) / 2;
                        drawY = 0;
                    } else {
                        // Image is taller than canvas
                        drawWidth = this.canvas.width;
                        drawHeight = drawWidth / imageAspect;
                        drawX = 0;
                        drawY = (this.canvas.height - drawHeight) / 2;
                    }
                    
                    this.ctx.drawImage(this.customBackgroundImage, drawX, drawY, drawWidth, drawHeight);
                    this.ctx.restore();
                    
                    customBackgroundRendered = true;
                } else if (this.customBackgroundImage && !this.customBackgroundImage.complete) {
                    // Image is still loading, but we want to show the custom background
                    // We'll show a placeholder or just not render the starfield
                    customBackgroundRendered = true;
                }
            }
        }
        
        // If no custom background, use AI service manager or fallback to default background
        if (!customBackgroundRendered) {
            if (this.aiServiceManager) {
                this.aiServiceManager.render(this.ctx, this.canvas.width, this.canvas.height);
            } else {
                // Fallback to default background image if available
                this.renderDefaultBackground();
            }
        }

        // Render player ship separately
        if (this.playerShip) {
            this.playerShip.render(this.ctx, interpolation);
        }

        // Render enemy manager (enemies and their projectiles)
        if (this.enemyManager) {
            this.enemyManager.render(this.ctx, interpolation);
        }

        // Render all other game objects (projectiles, enemies, etc.)
        if (this.gameObjectManager) {
            this.gameObjectManager.render(this.ctx, interpolation);
        }

        // Game title (smaller and positioned at top)
        this.ctx.fillStyle = '#ffffff';
        this.ctx.font = '20px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText('ChaosCruiser', this.canvas.width / 2, 30);

        // Display input debug info
        if (this.inputManager) {
            this.renderInputDebug();

            // Render input manager (virtual joystick, etc.)
            this.inputManager.render(this.ctx);
        }

        // FPS counter removed - not needed for gameplay

        // Display health and lives UI
        this.renderHealthAndLivesUI();

        // Display level and score UI
        this.renderLevelAndScoreUI();

        // Display token balance and reward animations
        if (this.tokenManager) {
            this.tokenManager.render(this.ctx, this.fixedTimeStep);
        }

        // Render Warp VFX overlays last (on top of everything)
        if (this.warpVFX) {
            this.warpVFX.render(this.ctx);
        }

        // Apply bloom effect as final post-processing step
        if (this.bloomEffect) {
            this.bloomEffect.applyBloom();
        }
    }

    /**
     * Change the current game environment
     * @param {string} environmentDescription - Description of the new environment
     */
    async changeEnvironment(environmentDescription) {
        if (this.aiServiceManager) {
            try {
                await this.aiServiceManager.createEnvironment(environmentDescription);
                console.log(`Environment changed to: ${environmentDescription}`);
            } catch (error) {
                console.error('Failed to change environment:', error);
            }
        }
    }

    /**
     * Get the current environment modifiers
     * @returns {Object} Current environment modifiers
     */
    getCurrentEnvironmentModifiers() {
        if (this.aiServiceManager) {
            return this.aiServiceManager.getCurrentEnvironmentModifiers();
        }
        return {};
    }

    /**
     * Apply environment modifiers to enemy stats
     * @param {Object} baseStats - Base enemy stats
     * @returns {Object} Modified stats with environment effects
     */
    applyEnvironmentModifiers(baseStats) {
        if (this.aiServiceManager) {
            return this.aiServiceManager.applyEnvironmentModifiers(baseStats);
        }
        return baseStats;
    }

    renderInputDebug() {
        // Input debug rendering removed - not needed for gameplay
        // This method is kept as empty function to maintain function calls
    }

    updateFPSCounter(frameTime) {
        this.frameCount++;
        this.fpsTimer += frameTime;

        // Update FPS display every second
        if (this.fpsTimer >= 1000) {
            this.currentFPS = Math.round((this.frameCount * 1000) / this.fpsTimer);
            this.frameCount = 0;
            this.fpsTimer = 0;
        }
    }

    renderDefaultBackground() {
        // Use default.jpg as the default background
        if (!this.defaultBackgroundImage) {
            // Create and load the default background image
            this.defaultBackgroundImage = new Image();
            this.defaultBackgroundImage.crossOrigin = 'anonymous';
            this.defaultBackgroundImage.onload = () => {
                // Image loaded successfully
            };
            this.defaultBackgroundImage.onerror = () => {
                console.error('Failed to load default background image');
                this.defaultBackgroundImage = null;
                // Fallback to starfield if default image fails to load
                this.renderStarField();
                return;
            };
            this.defaultBackgroundImage.src = '../server/images/default.jpg';
        }

        if (this.defaultBackgroundImage && this.defaultBackgroundImage.complete) {
            // Render default background
            this.ctx.save();
            
            // Calculate scaling to cover entire canvas while maintaining aspect ratio
            const imageAspect = this.defaultBackgroundImage.naturalWidth / this.defaultBackgroundImage.naturalHeight;
            const canvasAspect = this.canvas.width / this.canvas.height;
            
            let drawWidth, drawHeight, drawX, drawY;
            
            if (imageAspect > canvasAspect) {
                // Image is wider than canvas
                drawHeight = this.canvas.height;
                drawWidth = drawHeight * imageAspect;
                drawX = (this.canvas.width - drawWidth) / 2;
                drawY = 0;
            } else {
                // Image is taller than canvas
                drawWidth = this.canvas.width;
                drawHeight = drawWidth / imageAspect;
                drawX = 0;
                drawY = (this.canvas.height - drawHeight) / 2;
            }
            
            this.ctx.drawImage(this.defaultBackgroundImage, drawX, drawY, drawWidth, drawHeight);
            this.ctx.restore();
        } else {
            // Image is still loading or failed, fallback to starfield
            this.renderStarField();
        }
    }

    renderStarField() {
        // Simple star field effect
        this.ctx.fillStyle = '#ffffff';
        for (const star of this.stars) {
            this.ctx.fillRect(star.x, star.y, star.size, star.size);
        }
    }

    /**
     * Render health bar and lives counter UI
     */
    renderHealthAndLivesUI() {
        if (!this.playerShip) return;

        const healthStatus = this.playerShip.getHealthStatus();

        // Health bar position (top-right corner)
        const healthBarX = this.canvas.width - 220;
        const healthBarY = 15;
        const healthBarWidth = 200;
        const healthBarHeight = 20;

        // Health bar background
        this.ctx.fillStyle = '#333333';
        this.ctx.fillRect(healthBarX, healthBarY, healthBarWidth, healthBarHeight);

        // Health bar border
        this.ctx.strokeStyle = '#666666';
        this.ctx.lineWidth = 2;
        this.ctx.strokeRect(healthBarX, healthBarY, healthBarWidth, healthBarHeight);

        // Health bar fill
        const healthWidth = healthBarWidth * healthStatus.healthPercentage;
        let healthColor = '#00ff00'; // Green for healthy

        if (healthStatus.healthPercentage < 0.3) {
            healthColor = '#ff0000'; // Red for critical
        } else if (healthStatus.healthPercentage < 0.6) {
            healthColor = '#ffaa00'; // Orange for damaged
        }

        this.ctx.fillStyle = healthColor;
        this.ctx.fillRect(healthBarX + 2, healthBarY + 2, healthWidth - 4, healthBarHeight - 4);

        // Health text
        this.ctx.fillStyle = '#ffffff';
        this.ctx.font = '12px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(
            `${healthStatus.health}/${healthStatus.maxHealth}`,
            healthBarX + healthBarWidth / 2,
            healthBarY + healthBarHeight / 2 + 4
        );

        // Lives counter (below health bar)
        const livesY = healthBarY + healthBarHeight + 25;
        this.ctx.textAlign = 'right';
        this.ctx.fillStyle = '#ffffff';
        this.ctx.font = '16px Arial';
        this.ctx.fillText(`Lives: ${healthStatus.lives}`, this.canvas.width - 20, livesY);

        // Draw life icons
        const lifeIconSize = 16;
        const lifeIconSpacing = 20;
        const lifeIconStartX = this.canvas.width - 20 - (healthStatus.lives * lifeIconSpacing);

        for (let i = 0; i < healthStatus.lives; i++) {
            const iconX = lifeIconStartX + (i * lifeIconSpacing);
            const iconY = livesY + 10;

            // Draw small ship icon for each life
            this.ctx.fillStyle = '#4A90E2';
            this.ctx.beginPath();
            this.ctx.moveTo(iconX, iconY - lifeIconSize / 2);
            this.ctx.lineTo(iconX - lifeIconSize / 3, iconY + lifeIconSize / 3);
            this.ctx.lineTo(iconX + lifeIconSize / 3, iconY + lifeIconSize / 3);
            this.ctx.closePath();
            this.ctx.fill();
        }

        // Show invulnerability status
        if (healthStatus.isInvulnerable) {
            this.ctx.fillStyle = '#ffff00';
            this.ctx.font = '12px Arial';
            this.ctx.textAlign = 'right';
            const invulnTime = (healthStatus.invulnerabilityTimeRemaining / 1000).toFixed(1);
            this.ctx.fillText(`Invulnerable: ${invulnTime}s`, this.canvas.width - 20, livesY + 50);
        }

        // Show game over message if destroyed
        if (healthStatus.isDestroyed) {
            this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
            this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

            this.ctx.fillStyle = '#ff0000';
            this.ctx.font = '48px Arial';
            this.ctx.textAlign = 'center';
            this.ctx.fillText('GAME OVER', this.canvas.width / 2, this.canvas.height / 2);

            this.ctx.fillStyle = '#ffffff';
            this.ctx.font = '24px Arial';
            this.ctx.fillText('No lives remaining', this.canvas.width / 2, this.canvas.height / 2 + 50);
        }
    }

    /**
     * Render level and score UI
     */
    renderLevelAndScoreUI() {
        if (!this.levelManager) return;

        const levelStatus = this.levelManager.getLevelStatus();

        // Level information (top-left corner)
        this.ctx.fillStyle = '#ffffff';
        this.ctx.font = '16px Arial';
        this.ctx.textAlign = 'left';
        this.ctx.fillText(`Level: ${levelStatus.currentLevel}`, 10, 120);

        // Score display
        this.ctx.fillText(`Score: ${levelStatus.score.current.toLocaleString()}`, 10, 140);
        this.ctx.font = '12px Arial';
        this.ctx.fillText(`Level Score: ${levelStatus.score.level.toLocaleString()}`, 10, 155);

        // Progress display
        if (levelStatus.levelInProgress) {
            const progressText = `Enemies: ${levelStatus.progress.enemiesDefeated}/${levelStatus.progress.requiredEnemies}`;
            this.ctx.fillText(progressText, 10, 175);

            const waveText = `Waves: ${levelStatus.progress.wavesCompleted}/${levelStatus.progress.requiredWaves}`;
            this.ctx.fillText(waveText, 10, 190);

            // Timer and time limit warnings removed - not needed for gameplay
        }

        // Performance indicators removed - not needed for gameplay

        // Environment display
        if (levelStatus.levelConfig && levelStatus.levelConfig.environment) {
            this.ctx.fillStyle = '#cccccc';
            this.ctx.font = '12px Arial';
            const envText = `Environment: ${levelStatus.levelConfig.environment.toUpperCase()}`;
            this.ctx.fillText(envText, 10, 260);
        }

        // Level completion overlay
        if (!levelStatus.levelInProgress && levelStatus.levelConfig) {
            this.renderLevelCompletionOverlay(levelStatus);
        }
    }

    /**
     * Render level completion overlay
     * @param {object} levelStatus - Current level status
     */
    renderLevelCompletionOverlay(levelStatus) {
        // Semi-transparent overlay
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // Level complete text
        this.ctx.fillStyle = '#00ff00';
        this.ctx.font = '36px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(`Level ${levelStatus.currentLevel} Complete!`, this.canvas.width / 2, this.canvas.height / 2 - 50);

        // Score breakdown
        this.ctx.fillStyle = '#ffffff';
        this.ctx.font = '18px Arial';
        this.ctx.fillText(`Final Score: ${levelStatus.score.level.toLocaleString()}`, this.canvas.width / 2, this.canvas.height / 2);

        this.ctx.font = '14px Arial';
        this.ctx.fillText(`Enemies Defeated: ${levelStatus.progress.enemiesDefeated}`, this.canvas.width / 2, this.canvas.height / 2 + 25);

        const timeText = `Completion Time: ${(levelStatus.completionTime / 1000).toFixed(2)}s`;
        this.ctx.fillText(timeText, this.canvas.width / 2, this.canvas.height / 2 + 45);

        // Next level indicator
        this.ctx.fillStyle = '#cccccc';
        this.ctx.font = '12px Arial';
        this.ctx.fillText('Preparing next level...', this.canvas.width / 2, this.canvas.height / 2 + 80);
    }

    /**
     * Set up level manager callbacks
     */
    setupLevelManagerCallbacks() {
        // Level start callback
        this.levelManager.setOnLevelStart((levelNumber, levelConfig) => {
            console.log(`Level ${levelNumber} started:`, levelConfig);

            // Reset enemy manager for new level
            if (this.enemyManager) {
                this.enemyManager.reset();
                console.log('Enemy manager reset for new level');
            }
        });

        // Level completion callback
        this.levelManager.setOnLevelComplete((completionData) => {
            console.log('Level completed:', completionData);

            if (completionData.completed) {
                // Level completed successfully
                console.log(`Level ${completionData.levelNumber} completed in ${completionData.completionTime.toFixed(2)}s`);
                console.log(`Score: ${completionData.score.totalScore}, Enemies: ${completionData.enemiesDefeated}`);

                // Calculate and award WISH tokens
                if (this.tokenManager) {
                    const tokenReward = this.tokenManager.calculateLevelReward(completionData);
                    if (tokenReward.totalReward > 0) {
                        const awardResult = this.tokenManager.awardTokens(
                            tokenReward.totalReward,
                            'level_completion',
                            {
                                levelNumber: completionData.levelNumber,
                                completionTime: completionData.completionTime,
                                score: completionData.score.totalScore,
                                bonuses: completionData.bonuses,
                                breakdown: tokenReward.breakdown
                            }
                        );

                        console.log(`Awarded ${tokenReward.totalReward} WISH tokens for level completion`);
                        console.log('Token reward breakdown:', tokenReward.breakdown);

                        // Update completion data with token reward for Orange SDK
                        completionData.score.tokenReward = tokenReward.totalReward;
                    }
                }

                // Save progress to Orange SDK
                if (this.orangeSDKManager) {
                    this.orangeSDKManager.onLevelCompleted(completionData);
                }

                // Show Genie interface for power-up purchases
                this.showGenieInterface(completionData);
            } else {
                // Level failed
                console.log(`Level ${completionData.levelNumber} failed: ${completionData.reason}`);

                // Auto-retry after a brief delay
                setTimeout(() => {
                    if (completionData.canRetry) {
                        this.levelManager.startLevel(completionData.levelNumber);
                    }
                }, 3000);
            }
        });

        // Score update callback
        this.levelManager.setOnScoreUpdate((scoreData) => {
            // Update UI with new score information
            // This could trigger score display animations, etc.
        });
    }

    /**
     * Set up enemy manager callbacks
     */
    setupEnemyManagerCallbacks() {
        // Wave completion callback
        this.enemyManager.onWaveComplete = (waveNumber, bonus) => {
            console.log(`Wave ${waveNumber} completed with bonus: ${bonus}`);

            // Record wave completion in level manager
            if (this.levelManager) {
                this.levelManager.recordWaveCompletion(waveNumber, bonus);
            }
        };

        // Enemy escaped callback
        this.enemyManager.onEnemyEscaped = (enemy) => {
            console.log(`Enemy ${enemy.id} escaped off-screen`);

            // Record enemy escape in level manager (treat as defeated for progress)
            if (this.levelManager) {
                this.levelManager.recordEnemyDefeat(enemy, 0); // 0 score for escaped enemies
            }
        };
    }

    /**
     * Set up boss warp manager callbacks
     */
    setupBossWarpManagerCallbacks() {
        if (!this.bossWarpManager) return;

        // Set up callbacks for boss warp events
        this.bossWarpManager.onBossWarpStart = (warpData) => {
            console.log('Boss warp started:', warpData);
            // Handle boss warp start
        };

        this.bossWarpManager.onBossWarpComplete = (warpData) => {
            console.log('Boss warp completed:', warpData);
            // Handle boss warp completion
        };

        this.bossWarpManager.onBossWarpFailed = (warpData) => {
            console.log('Boss warp failed:', warpData);
            // Handle boss warp failure
        };
    }

    /**
     * Handle collisions between game objects
     */
    handleCollisions() {
        if (!this.enemyManager || !this.playerShip) return;
        
        // Debug: confirm handleCollisions is being called
        console.log('handleCollisions() called');

        // Check player-enemy collisions
        const playerCollisions = this.enemyManager.checkPlayerCollisions(this.playerShip);
        for (const enemy of playerCollisions) {
            const result = this.enemyManager.handlePlayerEnemyCollision(this.playerShip, enemy);
            console.log('Player-Enemy collision:', result);
        }

        // Check projectile-enemy collisions
        const projectiles = this.gameObjectManager.findByTag('projectile');
        const projectileCollisions = this.enemyManager.checkProjectileCollisions(projectiles);

        for (const collision of projectileCollisions) {
            const result = this.enemyManager.handleProjectileEnemyCollision(
                collision.projectile,
                collision.enemy,
                collision.damage
            );

            // Record enemy defeat in level manager
            if (result.enemyDestroyed && this.levelManager) {
                this.levelManager.recordEnemyDefeat(collision.enemy, result.scoreGained);
                
                // Spawn power-up collectible if dropped
                if (result.powerUpDropType) {
                    this.spawnPowerUpCollectible(result.powerUpDropType, result.enemyPosition);
                }
            }
        }

        // Check player-powerup collectible collisions
        this.gameObjectManager.checkCollisions('player', 'powerup-collectible', (player, collectible) => {
            console.log('Player collided with power-up collectible:', collectible.powerUpType);
            this.handlePowerUpCollection(collectible);
        });
    }

    /**
     * Spawn a power-up collectible in the game world
     * @param {string} powerUpType - Type of power-up to spawn
     * @param {Vector2} position - Position where to spawn the collectible
     */
    async spawnPowerUpCollectible(powerUpType, position) {
        try {
            // Create power-up collectible
            const { PowerUpCollectible } = await import('../entities/PowerUpCollectible.js');
            const collectible = new PowerUpCollectible(position.x, position.y, powerUpType);
            
            // Add to game object manager
            this.gameObjectManager.add(collectible);
            
            console.log(`Spawned ${powerUpType} power-up collectible at position: ${position.x}, ${position.y}`);
        } catch (error) {
            console.error('Error spawning power-up collectible:', error);
        }
    }

    /**
     * Handle power-up collection when player collides with collectible
     * @param {PowerUpCollectible} collectible - The collectible that was collected
     */
    async handlePowerUpCollection(collectible) {
        try {
            const powerUp = collectible.collect();
            if (!powerUp) {
                console.log('Power-up collection failed - no power-up returned from collectible');
                return;
            }
            
            // Apply the power-up to the player
            const success = await powerUp.apply(this.playerShip);
            
            if (success) {
                console.log(`Power-up ${powerUp.type} collected and applied successfully`);
                
                // Add to active power-ups for UI display
                if (this.genieInterface) {
                    this.genieInterface.activePowerUps.set(powerUp.type, powerUp);
                }
                
                // Show visual effect
                this.showPowerUpCollectEffect(collectible.position);
            } else {
                console.warn(`Failed to apply collected power-up ${powerUp.type}`);
            }
        } catch (error) {
            console.error('Error handling power-up collection:', error);
        }
    }

    /**
     * Show visual effect when collecting a power-up
     * @param {Vector2} position - Position where effect should appear
     */
    showPowerUpCollectEffect(position) {
        // Placeholder for visual effect - could be enhanced with particle system
        console.log(`Power-up collected at position: ${position.x}, ${position.y}`);
        // TODO: Add particle effect or visual indicator
    }

    /**
     * Set up token manager callbacks
     */
    setupTokenManagerCallbacks() {
        // Balance update callback
        this.tokenManager.setOnBalanceUpdate((balance, statistics) => {
            console.log(`Token balance updated: ${balance} WISH tokens`);

            // Notify Orange SDK of token changes
            if (this.orangeSDKManager) {
                this.orangeSDKManager.onTokensChanged({
                    balance: balance,
                    statistics: statistics
                });
            }
        });

        // Transaction callback
        this.tokenManager.setOnTransaction((transaction) => {
            console.log('Token transaction:', transaction);

            // Notify Orange SDK of significant token changes
            if (this.orangeSDKManager && transaction.amount >= 50) {
                const tokenData = transaction.type === 'earned'
                    ? { earned: transaction.amount }
                    : { spent: transaction.amount };

                this.orangeSDKManager.onTokensChanged(tokenData);
            }
        });

        // Reward earned callback
        this.tokenManager.setOnRewardEarned((amount, reason, metadata) => {
            console.log(`Token reward earned: ${amount} for ${reason}`);
        });
    }

    /**
     * Set up Genie interface callbacks
     */
    setupGenieInterfaceCallbacks() {
        // Power-up purchased callback
        this.genieInterface.setOnPowerUpPurchased((powerUp, transaction) => {
            console.log(`Power-up purchased: ${powerUp.type} for ${powerUp.cost} tokens`);

            // Update active power-ups tracking
            // The GenieInterface already handles the power-up application
        });

        // Reality warp purchased callback (for future implementation)
        this.genieInterface.setOnWarpPurchased((warpOption, transaction) => {
            console.log(`Reality warp purchased: ${warpOption.type} for ${warpOption.cost} tokens`);
        });

        // Interface closed callback
        this.genieInterface.setOnClose(() => {
            console.log('Genie interface closed, resuming game');
            this.continueToNextLevel();
        });
    }

    /**
     * Show the Genie interface between levels
     */
    showGenieInterface(completionData) {
        console.log('Showing Genie interface for level completion');

        // Store completion data for later use
        this.lastCompletionData = completionData;

        // Clear all active power-ups before showing the Genie interface
        this.clearPowerUps();

        // Pause the game
        this.pause();

        // Show the Genie interface
        this.genieInterface.show();
    }

    /**
     * Clear all active power-ups (called between levels)
     */
    clearPowerUps() {
        if (this.genieInterface && this.genieInterface.activePowerUps) {
            console.log('Clearing active power-ups for next level');

            const playerShip = this.playerShip;
            for (const [type, powerUp] of this.genieInterface.activePowerUps) {
                console.log(`Removing power-up: ${type}`);
                powerUp.remove(playerShip);
            }

            // Clear the active power-ups map
            this.genieInterface.activePowerUps.clear();

            console.log('All power-ups cleared');
        }
    }

    /**
     * Continue to the next level after Genie interface is closed
     */
    continueToNextLevel() {
        if (this.lastCompletionData) {
            const completionData = this.lastCompletionData;
            this.lastCompletionData = null;

            // Resume the game
            this.resume();

            // Continue to next level or end game
            if (completionData.nextLevel <= this.levelManager.maxLevels) {
                console.log(`Starting level ${completionData.nextLevel}`);
                this.levelManager.startLevel(completionData.nextLevel);
            } else {
                console.log('Game completed! All levels finished.');
                // TODO: Show game completion screen
            }
        }
    }

    /**
     * Test power-up system (debug method)
     */
    testPowerUpSystem() {
        console.log('Testing power-up system...');

        // Ensure player has tokens
        if (this.tokenManager.getBalance() < 500) {
            this.tokenManager.awardTokens(1000, 'debug_test_tokens');
            console.log('Added test tokens');
        }

        // Test each power-up type
        const powerUpTypes = ['EXTRA_LIFE', 'SPREAD_AMMO', 'EXTRA_WINGMAN'];

        powerUpTypes.forEach(async (type, index) => {
            setTimeout(async () => {
                console.log(`Testing ${type} power-up...`);

                if (this.genieInterface) {
                    await this.genieInterface.handlePowerUpPurchase(type);
                    console.log(`${type} test completed`);
                }
            }, index * 2000); // Stagger tests by 2 seconds
        });

        console.log('Power-up system test initiated');
    }

    /**
     * Initialize Orange SDK asynchronously
     */
    async initializeOrangeSDK() {
        try {
            await this.orangeSDKManager.initialize();
            console.log('Orange SDK initialized successfully');
        } catch (error) {
            console.error('Failed to initialize Orange SDK:', error);
        }
    }

    /**
     * Set up Orange SDK manager callbacks
     */
    setupOrangeSDKCallbacks() {
        // Set up special status callback to handle bonuses
        this.orangeSDKManager.setOnSpecialStatusCallback((statusType, value) => {
            this.handleSpecialStatus(statusType, value);
        });

        // Set up save success callback
        this.orangeSDKManager.setOnDataSavedCallback((data, reason) => {
            console.log(`Game data saved to Orange SDK (${reason})`);
        });

        // Set up save error callback
        this.orangeSDKManager.setOnSaveErrorCallback((error, reason) => {
            console.error(`Failed to save game data (${reason}):`, error);
        });
    }

    /**
     * Handle special status bonuses from Orange SDK
     * @param {string} statusType - Type of special status
     * @param {*} value - Status value
     */
    handleSpecialStatus(statusType, value) {
        switch (statusType) {
            case 'bonus_lives':
                if (this.playerShip && value > 0) {
                    this.playerShip.addLives(value);
                    console.log(`Bonus lives awarded: ${value} (login streak bonus)`);
                }
                break;

            case 'tournament_participant':
                if (value) {
                    console.log('Tournament participant status active');
                    // Could add special tournament UI indicators or bonuses
                }
                break;

            case 'achievement_unlock':
                console.log(`Achievement unlocked: ${value}`);
                // Could trigger achievement notification UI
                break;

            default:
                console.log(`Unknown special status: ${statusType} = ${value}`);
        }
    }

    handleResize() {
        const container = document.getElementById('game-container');
        if (container) {
            const { width, height } = container.getBoundingClientRect();
            this.canvas.width = width;
            this.canvas.height = height;
            console.log(`Canvas resized to: ${this.canvas.width}x${this.canvas.height}`);

            // Resize bloom effect canvases
            if (this.bloomEffect) {
                this.bloomEffect.resize();
            }
        }
    }

    /**
     * Initialize bloom debug controls
     */
    initBloomDebugControls() {
        document.addEventListener('keydown', (event) => {
            if (!this.bloomEffect) return;

            switch(event.key.toLowerCase()) {
                case 'b':
                    // Toggle bloom effect
                    const enabled = this.bloomEffect.toggle();
                    console.log(`Bloom effect ${enabled ? 'enabled' : 'disabled'}`);
                    break;
                case '=':
                case '+':
                    // Increase bloom intensity
                    const currentIntensity = this.bloomEffect.getSettings().intensity;
                    this.bloomEffect.setIntensity(currentIntensity + 0.1);
                    console.log(`Bloom intensity: ${this.bloomEffect.getSettings().intensity.toFixed(1)}`);
                    break;
                case '-':
                case '_':
                    // Decrease bloom intensity
                    const intensity = this.bloomEffect.getSettings().intensity;
                    this.bloomEffect.setIntensity(intensity - 0.1);
                    console.log(`Bloom intensity: ${this.bloomEffect.getSettings().intensity.toFixed(1)}`);
                    break;
                case '[':
                    // Decrease bloom threshold
                    const threshold = this.bloomEffect.getSettings().threshold;
                    this.bloomEffect.setThreshold(threshold - 0.05);
                    console.log(`Bloom threshold: ${this.bloomEffect.getSettings().threshold.toFixed(2)}`);
                    break;
                case ']':
                    // Increase bloom threshold
                    const currentThreshold = this.bloomEffect.getSettings().threshold;
                    this.bloomEffect.setThreshold(currentThreshold + 0.05);
                    console.log(`Bloom threshold: ${this.bloomEffect.getSettings().threshold.toFixed(2)}`);
                    break;
            }
        });
    }
}