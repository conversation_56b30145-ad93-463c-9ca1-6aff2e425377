import { GameObject } from '../utils/GameObject.js';
import { Vector2 } from '../utils/Vector2.js';
import { WeaponSystem } from '../systems/WeaponSystem.js';

/**
 * WingmanShip class - AI-controlled ship that follows the player and provides covering fire
 * Implements formation flying and autonomous targeting
 */
export class WingmanShip extends GameObject {
    constructor(x, y, playerShip, gameObjectManager) {
        super(x, y);
        
        // Reference to player ship for formation flying
        this.playerShip = playerShip;
        this.gameObjectManager = gameObjectManager;
        
        // Ship properties
        this.maxSpeed = 250; // Slightly slower than player
        this.acceleration = 600;
        this.friction = 0.9;
        
        // Formation flying properties
        this.formationOffset = new Vector2(-60, 20); // Position relative to player
        this.targetPosition = new Vector2(x, y);
        this.followDistance = 80;
        this.maxFormationDistance = 150;
        
        // AI behavior properties
        this.fireRate = 400; // Slightly slower than player
        this.lastFireTime = 0;
        this.targetingRange = 300;
        this.currentTarget = null;
        
        // Visual properties
        this.width = 48;
        this.height = 64;
        this.color = '#00ff88';
        this.glowColor = '#44ffaa';
        
        // Health and status
        this.maxHealth = 50;
        this.health = this.maxHealth;
        this.isDestroyed = false;
        
        // Animation properties
        this.animationTime = 0;
        this.thrusterIntensity = 0;
        
        // Weapon system
        this.weaponSystem = new WeaponSystem(this, gameObjectManager);
        this.weaponSystem.setFireRate(this.fireRate);
        this.weaponSystem.setProjectileDamage(20); // Slightly less damage than player
        this.weaponSystem.projectileType = 'wingman';
        
        // Add wingman tag
        this.addTag('wingman');
        this.addTag('friendly');
        
        
    }
    
    /**
     * Update wingman behavior, movement, and combat
     * @param {number} deltaTime - Time elapsed since last update in milliseconds
     */
    update(deltaTime) {
        if (!this.active || this.isDestroyed) return;
        
        // Update animation timer
        this.animationTime += deltaTime / 1000;
        
        // Update formation flying
        this.updateFormationFlying(deltaTime);
        
        // Update AI targeting and combat
        this.updateCombatAI(deltaTime);
        
        // Update weapon system
        if (this.weaponSystem) {
            this.weaponSystem.update(deltaTime);
        }
        
        // Update collision bounds
        this.updateCollisionBounds();
    }
    
    /**
     * Update formation flying behavior
     * @param {number} deltaTime - Time elapsed since last update
     */
    updateFormationFlying(deltaTime) {
        if (!this.playerShip || !this.playerShip.active) return;
        
        // Calculate desired formation position
        const playerPos = this.playerShip.position;
        const playerVel = this.playerShip.velocity;
        
        // Adjust formation offset based on player movement
        let dynamicOffset = this.formationOffset.clone();
        
        // If player is moving, adjust formation to be more behind
        if (playerVel.magnitude() > 50) {
            const playerDirection = playerVel.normalize();
            const sideOffset = new Vector2(-playerDirection.y, playerDirection.x).multiply(40);
            const backOffset = playerDirection.multiply(-30);
            dynamicOffset = sideOffset.add(backOffset);
        }
        
        this.targetPosition = playerPos.add(dynamicOffset);
        
        // Calculate distance to target position
        const toTarget = this.targetPosition.subtract(this.position);
        const distance = toTarget.magnitude();
        
        // Only move if we're too far from formation position
        if (distance > this.followDistance) {
            // Calculate movement force
            const desiredVelocity = toTarget.normalize().multiply(this.maxSpeed);
            const steeringForce = desiredVelocity.subtract(this.velocity);
            
            // Apply acceleration with smooth movement
            const acceleration = steeringForce.multiply(this.acceleration * deltaTime / 1000);
            this.velocity.addInPlace(acceleration);
            
            // Limit velocity
            if (this.velocity.magnitude() > this.maxSpeed) {
                this.velocity = this.velocity.normalize().multiply(this.maxSpeed);
            }
            
            this.thrusterIntensity = Math.min(1.0, distance / 100);
        } else {
            // Apply friction when in formation
            this.velocity.multiplyInPlace(Math.pow(this.friction, deltaTime / 16.67));
            this.thrusterIntensity *= 0.95;
        }
        
        // Update position
        this.position.addInPlace(this.velocity.multiply(deltaTime / 1000));
        
        // Keep wingman on screen (with some margin)
        const margin = 50;
        const canvasWidth = this.playerShip.canvasWidth || 800;
        const canvasHeight = this.playerShip.canvasHeight || 600;
        this.position.x = Math.max(margin, Math.min(this.position.x, canvasWidth - margin));
        this.position.y = Math.max(margin, Math.min(this.position.y, canvasHeight - margin));
    }
    
    /**
     * Update combat AI - targeting and firing
     * @param {number} deltaTime - Time elapsed since last update
     */
    updateCombatAI(deltaTime) {
        // Update fire cooldown
        this.lastFireTime += deltaTime;
        
        // Find nearest enemy target
        this.currentTarget = this.findNearestEnemy();
        
        // Fire at target if available and in range
        if (this.currentTarget && this.lastFireTime >= this.fireRate) {
            const distanceToTarget = this.position.distance(this.currentTarget.position);
            
            if (distanceToTarget <= this.targetingRange) {
                // Calculate firing direction
                const toTarget = this.currentTarget.position.subtract(this.position);
                const fireDirection = toTarget.normalize();
                
                // Fire weapon
                if (this.weaponSystem && this.weaponSystem.fire(fireDirection)) {
                    this.lastFireTime = 0;
                }
            }
        }
    }
    
    /**
     * Find the nearest enemy within targeting range
     * @returns {GameObject|null} Nearest enemy or null if none found
     */
    findNearestEnemy() {
        if (!this.gameObjectManager) return null;
        
        let nearestEnemy = null;
        let nearestDistance = this.targetingRange;
        
        // Get all enemies from game object manager
        const enemies = this.gameObjectManager.findByTag('enemy');
        
        for (const enemy of enemies) {
            if (!enemy.active) continue;
            
            const distance = this.position.distance(enemy.position);
            if (distance < nearestDistance) {
                nearestEnemy = enemy;
                nearestDistance = distance;
            }
        }
        
        return nearestEnemy;
    }
    
    /**
     * Take damage and handle destruction
     * @param {number} damage - Amount of damage to take
     * @returns {object} Damage result information
     */
    takeDamage(damage) {
        if (this.isDestroyed) {
            return {
                damageTaken: 0,
                destroyed: true,
                scoreValue: 0
            };
        }
        
        const actualDamage = Math.max(0, damage);
        this.health -= actualDamage;
        
        const result = {
            damageTaken: actualDamage,
            destroyed: false,
            scoreValue: 0
        };
        
        if (this.health <= 0) {
            this.health = 0;
            this.destroy();
            result.destroyed = true;
        }
        
        return result;
    }
    
    /**
     * Destroy the wingman ship
     */
    destroy() {
        this.isDestroyed = true;
        this.active = false;
        
        // TODO: Add destruction visual effects
        
    }
    
    /**
     * Render the wingman ship
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     * @param {number} interpolation - Interpolation factor for smooth rendering
     */
    render(ctx, interpolation = 0) {
        if (!this.active || this.isDestroyed) return;

        // Calculate interpolated position for smooth rendering
        const interpolationTimeSeconds = (interpolation * 16.67) / 1000; // 16.67ms is the fixed timestep
        const renderPos = this.position.add(this.velocity.multiply(interpolationTimeSeconds));

        ctx.save();
        ctx.translate(renderPos.x, renderPos.y);
        
        // Draw ship glow effect
        if (this.thrusterIntensity > 0.1) {
            ctx.shadowColor = this.glowColor;
            ctx.shadowBlur = 15 * this.thrusterIntensity;
        }
        
        // Use sprite if available, otherwise fall back to original drawing
        const sprite = this.getWingmanSprite();
        if (sprite) {
            // Draw sprite with bloom effect
            const spriteSize = this.width * 1.5; // Slightly larger than the original ship
            
            // Apply shadow blur for bloom effect
            ctx.shadowColor = this.color;
            ctx.shadowBlur = 15;
            
            ctx.drawImage(
                sprite,
                -spriteSize / 2,
                -spriteSize / 2,
                spriteSize,
                spriteSize
            );
            
            ctx.shadowBlur = 0;
        } else {
            // Fallback to original ship drawing if sprite not available
            // Draw main ship body
            ctx.fillStyle = this.color;
            ctx.beginPath();
            ctx.moveTo(0, -this.height / 2);
            ctx.lineTo(-this.width / 3, this.height / 3);
            ctx.lineTo(-this.width / 6, this.height / 2);
            ctx.lineTo(this.width / 6, this.height / 2);
            ctx.lineTo(this.width / 3, this.height / 3);
            ctx.closePath();
            ctx.fill();

            // Draw ship details
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(-2, -this.height / 4, 4, 8);

            // Draw thrusters
            if (this.thrusterIntensity > 0.1) {
                const thrusterLength = 15 * this.thrusterIntensity;
                const thrusterFlicker = Math.sin(this.animationTime * 20) * 0.3 + 0.7;
                
                ctx.fillStyle = `rgba(0, 150, 255, ${thrusterFlicker * this.thrusterIntensity})`;
                ctx.fillRect(-4, this.height / 2, 3, thrusterLength);
                ctx.fillRect(1, this.height / 2, 3, thrusterLength);
            }
        }
        
        // Render weapon effects
        if (this.weaponSystem) {
            this.weaponSystem.render(ctx);
        }
        
        ctx.restore();
    }
    
    /**
     * Update collision bounds
     */
    updateCollisionBounds() {
        this.bounds = {
            left: this.position.x - this.width / 2,
            right: this.position.x + this.width / 2,
            top: this.position.y - this.height / 2,
            bottom: this.position.y + this.height / 2
        };
    }
    
    /**
     * Get wingman status information
     * @returns {object} Status information
     */
    getStatus() {
        return {
            health: this.health,
            maxHealth: this.maxHealth,
            healthPercentage: this.health / this.maxHealth,
            isDestroyed: this.isDestroyed,
            hasTarget: !!this.currentTarget,
            distanceToPlayer: this.playerShip ? this.position.distance(this.playerShip.position) : 0
        };
    }

    /**
     * Fire weapon in the same direction as the player
     * @param {Vector2} direction - Direction to fire (from player)
     * @returns {boolean} True if weapon fired successfully
     */
    fireOnPlayerFire(direction) {
        if (!this.active || this.isDestroyed) return false;
        
        // Check if weapon is ready
        if (this.weaponSystem && this.weaponSystem.isReady()) {
            // Fire in the same direction as player
            return this.weaponSystem.fire(direction);
        }
        return false;
    }

    /**
     * Get the wingman sprite from the game engine
     * @returns {Image|null} Wingman sprite or null if not loaded
     */
    getWingmanSprite() {
        if (window.gameEngine && window.gameEngine.wingmanSprite) {
            const sprite = window.gameEngine.wingmanSprite;
            if (sprite && sprite.complete) {
                return sprite;
            }
        }
        return null;
    }
}
