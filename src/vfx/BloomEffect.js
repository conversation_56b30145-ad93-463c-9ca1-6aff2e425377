/**
 * BloomEffect - Post-processing bloom effect for enhanced graphics
 * 
 * This system creates a bloom effect by:
 * 1. Rendering bright elements to an off-screen canvas
 * 2. Applying gaussian blur to create the glow
 * 3. Compositing the blurred result back onto the main canvas
 * 
 * Features:
 * - Configurable bloom intensity and threshold
 * - Performance optimized with downsampling
 * - Automatic bright element detection
 * - Smooth blending with original scene
 */

export class BloomEffect {
    constructor(canvas, options = {}) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        
        // Configuration
        this.enabled = options.enabled !== false; // Default enabled
        this.intensity = options.intensity || 0.8;
        this.threshold = options.threshold || 0.6; // Brightness threshold (0-1)
        this.blurRadius = options.blurRadius || 15;
        this.downsample = options.downsample || 2; // Render bloom at 1/2 resolution for performance
        
        // Off-screen canvases for bloom processing
        this.brightCanvas = null;
        this.brightCtx = null;
        this.blurCanvas = null;
        this.blurCtx = null;
        
        // Initialize off-screen canvases
        this.initializeCanvases();
        
        // Track elements that should bloom
        this.bloomElements = new Set();
        
        // Performance tracking
        this.lastFrameTime = 0;
        this.skipFrames = 0; // Skip bloom on some frames if performance is poor
    }
    
    /**
     * Initialize off-screen canvases for bloom processing
     */
    initializeCanvases() {
        const width = Math.floor(this.canvas.width / this.downsample);
        const height = Math.floor(this.canvas.height / this.downsample);
        
        // Canvas for extracting bright elements
        this.brightCanvas = document.createElement('canvas');
        this.brightCanvas.width = width;
        this.brightCanvas.height = height;
        this.brightCtx = this.brightCanvas.getContext('2d');
        
        // Canvas for blur processing
        this.blurCanvas = document.createElement('canvas');
        this.blurCanvas.width = width;
        this.blurCanvas.height = height;
        this.blurCtx = this.blurCanvas.getContext('2d');
    }
    
    /**
     * Resize bloom canvases when main canvas changes size
     */
    resize() {
        if (this.canvas.width !== this.lastWidth || this.canvas.height !== this.lastHeight) {
            this.initializeCanvases();
            this.lastWidth = this.canvas.width;
            this.lastHeight = this.canvas.height;
        }
    }
    
    /**
     * Register an element type that should contribute to bloom
     * @param {string} elementType - Type of element (e.g., 'projectile', 'explosion', 'engine')
     */
    addBloomElement(elementType) {
        this.bloomElements.add(elementType);
    }
    
    /**
     * Remove an element type from bloom processing
     * @param {string} elementType - Type of element to remove
     */
    removeBloomElement(elementType) {
        this.bloomElements.delete(elementType);
    }
    
    /**
     * Apply bloom effect to the current canvas content
     * This should be called after all game objects are rendered
     */
    applyBloom() {
        if (!this.enabled) return;
        
        const startTime = performance.now();
        
        // Skip bloom on some frames if performance is poor
        if (this.skipFrames > 0) {
            this.skipFrames--;
            return;
        }
        
        this.resize();
        
        // Step 1: Extract bright areas from the main canvas
        this.extractBrightAreas();
        
        // Step 2: Apply blur to create bloom effect
        this.applyBlur();
        
        // Step 3: Composite bloom back onto main canvas
        this.compositeBloom();
        
        // Performance monitoring
        const frameTime = performance.now() - startTime;
        if (frameTime > 8) { // If bloom takes more than 8ms, skip next frame
            this.skipFrames = 1;
        }
    }
    
    /**
     * Extract bright areas from the main canvas
     */
    extractBrightAreas() {
        // Clear the bright canvas
        this.brightCtx.clearRect(0, 0, this.brightCanvas.width, this.brightCanvas.height);
        
        // Scale down and copy main canvas
        this.brightCtx.save();
        this.brightCtx.scale(1 / this.downsample, 1 / this.downsample);
        this.brightCtx.drawImage(this.canvas, 0, 0);
        this.brightCtx.restore();
        
        // Apply brightness threshold filter
        this.applyBrightnessThreshold();
    }
    
    /**
     * Apply brightness threshold to isolate bright areas
     */
    applyBrightnessThreshold() {
        const imageData = this.brightCtx.getImageData(0, 0, this.brightCanvas.width, this.brightCanvas.height);
        const data = imageData.data;
        
        for (let i = 0; i < data.length; i += 4) {
            const r = data[i] / 255;
            const g = data[i + 1] / 255;
            const b = data[i + 2] / 255;
            const a = data[i + 3] / 255;
            
            // Calculate luminance
            const luminance = 0.299 * r + 0.587 * g + 0.114 * b;
            
            // Apply threshold
            if (luminance < this.threshold) {
                data[i] = 0;     // R
                data[i + 1] = 0; // G
                data[i + 2] = 0; // B
                data[i + 3] = 0; // A
            } else {
                // Enhance bright areas
                const enhancement = Math.pow((luminance - this.threshold) / (1 - this.threshold), 0.8);
                data[i] = Math.min(255, data[i] * enhancement * 1.2);
                data[i + 1] = Math.min(255, data[i + 1] * enhancement * 1.2);
                data[i + 2] = Math.min(255, data[i + 2] * enhancement * 1.2);
            }
        }
        
        this.brightCtx.putImageData(imageData, 0, 0);
    }
    
    /**
     * Apply gaussian blur to create bloom effect
     */
    applyBlur() {
        // Clear blur canvas
        this.blurCtx.clearRect(0, 0, this.blurCanvas.width, this.blurCanvas.height);
        
        // Apply horizontal blur
        this.blurCtx.filter = `blur(${this.blurRadius}px)`;
        this.blurCtx.drawImage(this.brightCanvas, 0, 0);
        this.blurCtx.filter = 'none';
    }
    
    /**
     * Composite bloom effect back onto main canvas
     */
    compositeBloom() {
        this.ctx.save();
        
        // Use additive blending for bloom effect
        this.ctx.globalCompositeOperation = 'screen';
        this.ctx.globalAlpha = this.intensity;
        
        // Scale bloom back up to full resolution
        this.ctx.drawImage(
            this.blurCanvas,
            0, 0, this.blurCanvas.width, this.blurCanvas.height,
            0, 0, this.canvas.width, this.canvas.height
        );
        
        this.ctx.restore();
    }
    
    /**
     * Toggle bloom effect on/off
     */
    toggle() {
        this.enabled = !this.enabled;
        return this.enabled;
    }
    
    /**
     * Set bloom intensity
     * @param {number} intensity - Bloom intensity (0-2)
     */
    setIntensity(intensity) {
        this.intensity = Math.max(0, Math.min(2, intensity));
    }
    
    /**
     * Set brightness threshold
     * @param {number} threshold - Brightness threshold (0-1)
     */
    setThreshold(threshold) {
        this.threshold = Math.max(0, Math.min(1, threshold));
    }
    
    /**
     * Get current bloom settings
     */
    getSettings() {
        return {
            enabled: this.enabled,
            intensity: this.intensity,
            threshold: this.threshold,
            blurRadius: this.blurRadius
        };
    }
    
    /**
     * Cleanup resources
     */
    destroy() {
        this.brightCanvas = null;
        this.brightCtx = null;
        this.blurCanvas = null;
        this.blurCtx = null;
        this.bloomElements.clear();
    }
}
